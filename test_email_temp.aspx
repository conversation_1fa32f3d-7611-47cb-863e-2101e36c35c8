<%@ Page Language="C#" %>
<!DOCTYPE html>
<html>
<head>
    <title>Test Email Configuration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
        .config { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔧 Test Email Configuration - SQUBY</h1>
    
    <script runat="server">
        protected void Page_Load(object sender, EventArgs e)
        {
            if (Request.QueryString["send"] == "1")
            {
                try
                {
                    // Test email sending using the Utility.SendMail method
                    Utility.SendMail("<EMAIL>", "<EMAIL>", 
                        "Test Email da SQUBY - " + DateTime.Now.ToString("dd/MM/yyyy HH:mm"), 
                        "<h1>✅ Test Email da SQUBY</h1>" +
                        "<p>Questa è una email di test inviata da SQUBY con la nuova configurazione SMTP.</p>" +
                        "<p><strong>Data invio:</strong> " + DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss") + "</p>" +
                        "<p><strong>Server:</strong> " + Request.ServerVariables["SERVER_NAME"] + "</p>" +
                        "<p>Se ricevi questa email, la configurazione SMTP funziona correttamente! 🎉</p>", 
                        null, null, null);
                    
                    Response.Write("<div class='success'>✅ Email inviata con <NAME_EMAIL>!</div>");
                    Response.Write("<p>Controlla la casella di posta (anche spam) per verificare la ricezione.</p>");
                }
                catch (Exception ex)
                {
                    Response.Write("<div class='error'>❌ Errore nell'invio email:</div>");
                    Response.Write("<div class='error'><pre>" + Server.HtmlEncode(ex.Message) + "</pre></div>");
                    if (ex.InnerException != null)
                    {
                        Response.Write("<div class='error'><strong>Dettaglio:</strong> " + Server.HtmlEncode(ex.InnerException.Message) + "</div>");
                    }
                }
            }
            
            // Display current SMTP configuration
            Response.Write("<div class='config'>");
            Response.Write("<h2>📧 Configurazione SMTP Attuale:</h2>");
            Response.Write("<ul>");
            try 
            {
                Response.Write("<li><strong>SMTP Server:</strong> " + Utility.GetKeyWeb("SmtpEmail") + "</li>");
                Response.Write("<li><strong>SMTP Port:</strong> " + Utility.GetKeyWeb("SmtpPort") + "</li>");
                Response.Write("<li><strong>SMTP SSL:</strong> " + Utility.GetKeyWeb("SmtpSsl") + "</li>");
                Response.Write("<li><strong>From Email:</strong> " + Utility.GetKeyWeb("SendfromEmail") + "</li>");
                Response.Write("<li><strong>Contact Form To:</strong> " + Utility.GetKeyWeb("ContactFormTo") + "</li>");
                Response.Write("<li><strong>Documents Path:</strong> " + Utility.GetKeyWeb("PathDocs") + "</li>");
            }
            catch (Exception ex)
            {
                Response.Write("<li class='error'>Errore nel leggere la configurazione: " + Server.HtmlEncode(ex.Message) + "</li>");
            }
            Response.Write("</ul>");
            Response.Write("</div>");
        }
    </script>
    
    <p>
        <a href="test_email.aspx?send=1" class="button">
            📧 Invia Email di <NAME_EMAIL>
        </a>
    </p>
    
    <p><a href="admin/login.aspx">← Torna al Login Admin</a></p>
    
    <hr>
    <p><small>Pagina di test creata il <%= DateTime.Now.ToString("dd/MM/yyyy HH:mm") %></small></p>
</body>
</html>
