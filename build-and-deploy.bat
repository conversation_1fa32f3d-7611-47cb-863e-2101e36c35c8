@echo off
echo ========================================
echo iGrest Build and Deploy Script
echo ========================================

set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
set PROJECT_PATH=C:\Users\<USER>\iGrest
set DEPLOY_PATH=C:\websites\squby.it.live

echo.
echo Step 1: Building solution...
cd /d "%PROJECT_PATH%"
%MSBUILD_PATH% iGrest.sln /p:Configuration=Debug /p:Platform="Any CPU"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Copying compiled files to IIS directory...

echo Copying iGrest.Core.dll...
copy "%PROJECT_PATH%\iGrest.WebSite\Bin\iGrest.Core.dll" "%DEPLOY_PATH%\Bin\iGrest.Core.dll" /Y

echo Copying iGrest.Core.pdb...
copy "%PROJECT_PATH%\iGrest.WebSite\Bin\iGrest.Core.pdb" "%DEPLOY_PATH%\Bin\iGrest.Core.pdb" /Y

echo Copying App_Code files...
xcopy "%PROJECT_PATH%\iGrest.WebSite\App_Code\*.*" "%DEPLOY_PATH%\App_Code\" /Y /S /Q

echo Copying other dependencies...
xcopy "%PROJECT_PATH%\iGrest.WebSite\Bin\*.dll" "%DEPLOY_PATH%\Bin\" /Y /Q
xcopy "%PROJECT_PATH%\iGrest.WebSite\Bin\*.pdb" "%DEPLOY_PATH%\Bin\" /Y /Q

echo.
echo Step 3: Updating web.config with production settings...
copy "%PROJECT_PATH%\web.config" "%DEPLOY_PATH%\web.config" /Y

echo.
echo ========================================
echo Build and Deploy completed successfully!
echo ========================================
echo.
echo Files deployed to: %DEPLOY_PATH%
echo.
pause
